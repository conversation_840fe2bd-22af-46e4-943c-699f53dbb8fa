<!-- Contact Form Content -->
<form id="contact-form" class="space-y-5" novalidate>
    <!-- Hidden Fields -->
    <input type="hidden" id="contactId" name="id" value="{% if curContact %}{{ curContact.id }}{% endif %}">

    <!-- Contact Type Selection -->
    <div class="space-y-2">
        <label for="contactType" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
            Tipo Contatto <span class="text-red-500">*</span>
        </label>
        <select id="contactType" name="contactType" class="py-2 px-3 pe-9 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" required>
            <option value="">Seleziona tipo contatto</option>
            <option value="PERSON" {% if curContact and curContact.contactType.name() == 'PERSON' %}selected{% endif %}>Persona</option>
            <option value="COMPANY" {% if curContact and curContact.contactType.name() == 'COMPANY' %}selected{% endif %}>Azienda</option>
        </select>
        <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
            Seleziona un tipo di contatto.
        </div>
    </div>

    <!-- Person Fields (shown when contactType is PERSON) -->
    <div id="person-fields" class="space-y-5 hidden">
        <!-- First Name -->
        <div class="space-y-2">
            <label for="firstName" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Nome <span class="text-red-500">*</span>
            </label>
            <input type="text" id="firstName" name="firstName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il nome" value="{% if curContact %}{{ curContact.firstName }}{% endif %}">
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Il nome è obbligatorio per le persone.
            </div>
        </div>

        <!-- Last Name -->
        <div class="space-y-2">
            <label for="lastName" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Cognome <span class="text-red-500">*</span>
            </label>
            <input type="text" id="lastName" name="lastName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il cognome" value="{% if curContact %}{{ curContact.lastName }}{% endif %}">
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Il cognome è obbligatorio per le persone.
            </div>
        </div>
    </div>

    <!-- Company Fields (shown when contactType is COMPANY) -->
    <div id="company-fields" class="space-y-5 hidden">
        <!-- Company Name -->
        <div class="space-y-2">
            <label for="companyName" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Ragione Sociale <span class="text-red-500">*</span>
            </label>
            <input type="text" id="companyName" name="companyName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci la ragione sociale" value="{% if curContact %}{{ curContact.companyName }}{% endif %}">
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                La ragione sociale è obbligatoria per le aziende.
            </div>
        </div>

        <!-- VAT Number -->
        <div class="space-y-2">
            <label for="vatNumber" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Partita IVA
            </label>
            <input type="text" id="vatNumber" name="vatNumber" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci la partita IVA" value="{% if curContact %}{{ curContact.vatNumber }}{% endif %}">
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Inserisci una partita IVA valida.
            </div>
        </div>
    </div>

    <!-- Common Fields -->
    <div class="space-y-5">
        <!-- Email -->
        <div class="space-y-2">
            <label for="email" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Email <span class="text-red-500">*</span>
            </label>
            <input type="email" id="email" name="email" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci l'email" value="{% if curContact %}{{ curContact.email }}{% endif %}" required>
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Inserisci un indirizzo email valido.
            </div>
        </div>

        <!-- Phone Number -->
        <div class="space-y-2">
            <label for="phoneNumber" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Numero di Telefono
            </label>
            <input type="tel" id="phoneNumber" name="phoneNumber" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il numero di telefono" value="{% if curContact %}{{ curContact.phoneNumber }}{% endif %}">
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Inserisci un numero di telefono valido.
            </div>
        </div>

        <!-- Associated Users - Preline Advanced Select -->
        <div class="space-y-2">
            <label for="userIds" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Utenti Associati
            </label>
            <div class="relative">
                <select id="userIds" name="userIds" multiple data-hs-select='{
                      "placeholder": "Seleziona Utenti...",
                      "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                      "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-3 ps-4 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                      "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                      "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                      "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                      "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden">
                    <option value="">Seleziona utenti...</option>
                    {% if allUsers is not empty %}
                        {% for user in allUsers %}
                            <option value="{{ user.id }}"
                                {% if curContact is not empty and curContact.userIds is not empty and curContact.userIds contains user.id %}selected{% endif %}>
                                {{ user.firstName }} {{ user.lastName }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Seleziona almeno un utente associato.
            </div>
        </div>

        <!-- Note -->
        <div class="space-y-2">
            <label for="note" class="inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                Note
            </label>
            <textarea id="note" name="note" rows="4" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci eventuali note...">{% if curContact %}{{ curContact.note }}{% endif %}</textarea>
            <div class="invalid-feedback text-sm text-red-600 mt-1 hidden">
                Le note non possono superare i 1000 caratteri.
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end items-center gap-x-2 py-3 px-4 border-t dark:border-neutral-700">
        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-dutoo">
            Annulla
        </button>
        <button type="submit" id="save-contact-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
            <span class="save-btn-text">{% if curContact %}Aggiorna{% else %}Salva{% endif %}</span>
            <span class="save-btn-spinner hidden">
                <svg class="animate-spin size-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </span>
        </button>
    </div>
</form>
