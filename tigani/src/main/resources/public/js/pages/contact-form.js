// Contact Form Management
const ContactForm = function () {

    // Form validation and submission
    const _componentFormValidation = function () {
        const form = document.getElementById('contact-form');
        if (!form) {
            console.warn('Contact form not found');
            return;
        }

        // Contact type change handler
        const contactTypeSelect = document.getElementById('contactType');
        if (contactTypeSelect) {
            contactTypeSelect.addEventListener('change', _handleContactTypeChange);
            // Initialize on page load
            _handleContactTypeChange.call(contactTypeSelect);
        }

        // Form submission handler
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (_validateForm()) {
                _submitForm();
            }
        });

        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                _validateField(this);
            });

            input.addEventListener('input', function() {
                _clearFieldError(this);
            });
        });
    };

    // Handle contact type change to show/hide relevant fields
    function _handleContactTypeChange() {
        const contactType = this.value;
        const personFields = document.getElementById('person-fields');
        const companyFields = document.getElementById('company-fields');

        if (contactType === 'PERSON') {
            personFields.classList.remove('hidden');
            companyFields.classList.add('hidden');
            
            // Make person fields required
            document.getElementById('firstName').required = true;
            document.getElementById('lastName').required = true;
            
            // Make company fields not required
            document.getElementById('companyName').required = false;
        } else if (contactType === 'COMPANY') {
            personFields.classList.add('hidden');
            companyFields.classList.remove('hidden');
            
            // Make company fields required
            document.getElementById('companyName').required = true;
            
            // Make person fields not required
            document.getElementById('firstName').required = false;
            document.getElementById('lastName').required = false;
        } else {
            // No type selected - hide both
            personFields.classList.add('hidden');
            companyFields.classList.add('hidden');
            
            // Make all conditional fields not required
            document.getElementById('firstName').required = false;
            document.getElementById('lastName').required = false;
            document.getElementById('companyName').required = false;
        }
    }

    // Validate entire form
    function _validateForm() {
        let isValid = true;
        const form = document.getElementById('contact-form');
        
        // Clear all previous errors
        _clearAllErrors();

        // Validate contact type
        const contactType = document.getElementById('contactType');
        if (!contactType.value) {
            _showFieldError(contactType, 'Seleziona un tipo di contatto.');
            isValid = false;
        }

        // Validate based on contact type
        if (contactType.value === 'PERSON') {
            const firstName = document.getElementById('firstName');
            const lastName = document.getElementById('lastName');
            
            if (!firstName.value.trim()) {
                _showFieldError(firstName, 'Il nome è obbligatorio per le persone.');
                isValid = false;
            }
            
            if (!lastName.value.trim()) {
                _showFieldError(lastName, 'Il cognome è obbligatorio per le persone.');
                isValid = false;
            }
        } else if (contactType.value === 'COMPANY') {
            const companyName = document.getElementById('companyName');
            
            if (!companyName.value.trim()) {
                _showFieldError(companyName, 'La ragione sociale è obbligatoria per le aziende.');
                isValid = false;
            }

            // Validate VAT number if provided
            const vatNumber = document.getElementById('vatNumber');
            if (vatNumber.value.trim() && !_isValidVatNumber(vatNumber.value.trim())) {
                _showFieldError(vatNumber, 'Inserisci una partita IVA valida.');
                isValid = false;
            }
        }

        // Validate email
        const email = document.getElementById('email');
        if (!email.value.trim()) {
            _showFieldError(email, 'L\'email è obbligatoria.');
            isValid = false;
        } else if (!_isValidEmail(email.value.trim())) {
            _showFieldError(email, 'Inserisci un indirizzo email valido.');
            isValid = false;
        }

        // Validate phone number if provided
        const phoneNumber = document.getElementById('phoneNumber');
        if (phoneNumber.value.trim() && !_isValidPhoneNumber(phoneNumber.value.trim())) {
            _showFieldError(phoneNumber, 'Inserisci un numero di telefono valido.');
            isValid = false;
        }

        // Validate note length
        const note = document.getElementById('note');
        if (note.value.length > 1000) {
            _showFieldError(note, 'Le note non possono superare i 1000 caratteri.');
            isValid = false;
        }

        return isValid;
    }

    // Validate individual field
    function _validateField(field) {
        _clearFieldError(field);

        switch (field.id) {
            case 'contactType':
                if (!field.value) {
                    _showFieldError(field, 'Seleziona un tipo di contatto.');
                    return false;
                }
                break;
            case 'firstName':
                if (document.getElementById('contactType').value === 'PERSON' && !field.value.trim()) {
                    _showFieldError(field, 'Il nome è obbligatorio per le persone.');
                    return false;
                }
                break;
            case 'lastName':
                if (document.getElementById('contactType').value === 'PERSON' && !field.value.trim()) {
                    _showFieldError(field, 'Il cognome è obbligatorio per le persone.');
                    return false;
                }
                break;
            case 'companyName':
                if (document.getElementById('contactType').value === 'COMPANY' && !field.value.trim()) {
                    _showFieldError(field, 'La ragione sociale è obbligatoria per le aziende.');
                    return false;
                }
                break;
            case 'vatNumber':
                if (field.value.trim() && !_isValidVatNumber(field.value.trim())) {
                    _showFieldError(field, 'Inserisci una partita IVA valida.');
                    return false;
                }
                break;
            case 'email':
                if (!field.value.trim()) {
                    _showFieldError(field, 'L\'email è obbligatoria.');
                    return false;
                } else if (!_isValidEmail(field.value.trim())) {
                    _showFieldError(field, 'Inserisci un indirizzo email valido.');
                    return false;
                }
                break;
            case 'phoneNumber':
                if (field.value.trim() && !_isValidPhoneNumber(field.value.trim())) {
                    _showFieldError(field, 'Inserisci un numero di telefono valido.');
                    return false;
                }
                break;
            case 'note':
                if (field.value.length > 1000) {
                    _showFieldError(field, 'Le note non possono superare i 1000 caratteri.');
                    return false;
                }
                break;
        }

        return true;
    }

    // Submit form
    function _submitForm() {
        const form = document.getElementById('contact-form');
        const saveBtn = document.getElementById('save-contact-btn');
        const saveBtnText = saveBtn.querySelector('.save-btn-text');
        const saveBtnSpinner = saveBtn.querySelector('.save-btn-spinner');

        // Show loading state
        saveBtn.disabled = true;
        saveBtnText.classList.add('hidden');
        saveBtnSpinner.classList.remove('hidden');

        // Prepare form data
        const formData = new FormData(form);

        // Handle userIds array - get selected values from the advanced select
        const userIdsSelect = document.getElementById('userIds');
        const selectedUserIds = Array.from(userIdsSelect.selectedOptions).map(option => option.value);
        
        // Remove existing userIds entries and add the array
        formData.delete('userIds');
        selectedUserIds.forEach(userId => {
            if (userId) { // Only add non-empty values
                formData.append('userIds', userId);
            }
        });

        // Get contact ID for edit mode
        const contactId = document.getElementById('contactId').value;
        const url = contactId ? 
            `${appRoutes.get('BE_CONTACT_SAVE')}?contactId=${encodeURIComponent(contactId)}` : 
            appRoutes.get('BE_CONTACT_SAVE');

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Reset loading state
                saveBtn.disabled = false;
                saveBtnText.classList.remove('hidden');
                saveBtnSpinner.classList.add('hidden');

                // Show success message
                showToast('Contatto salvato correttamente.', 'success');

                // Reload the contacts table if it exists
                if (typeof ContactCollection !== 'undefined' && ContactCollection.reloadTable) {
                    ContactCollection.reloadTable();
                }

                // Close the offcanvas
                const offcanvasElement = document.querySelector('[data-hs-overlay].hs-overlay-open');
                if (offcanvasElement) {
                    HSOverlay.close(offcanvasElement);
                }
            },
            error: function(xhr, status, error) {
                // Reset loading state
                saveBtn.disabled = false;
                saveBtnText.classList.remove('hidden');
                saveBtnSpinner.classList.add('hidden');

                // Show error message
                const errorMessage = xhr.responseText || 'Errore durante il salvataggio del contatto.';
                showToast(errorMessage, 'error');
                console.error('Error saving contact:', error);
            }
        });
    }

    // Validation helper functions
    function _isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function _isValidPhoneNumber(phone) {
        // Basic phone number validation - allows various formats
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,20}$/;
        return phoneRegex.test(phone);
    }

    function _isValidVatNumber(vatNumber) {
        // Basic Italian VAT number validation (11 digits)
        const vatRegex = /^[0-9]{11}$/;
        return vatRegex.test(vatNumber.replace(/\s/g, ''));
    }

    // Error handling functions
    function _showFieldError(field, message) {
        // Add error class to field
        field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');

        // Show error message
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }
    }

    function _clearFieldError(field) {
        // Remove error classes
        field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.add('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');

        // Hide error message
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
    }

    function _clearAllErrors() {
        const form = document.getElementById('contact-form');
        const errorFields = form.querySelectorAll('.border-red-500');
        const errorMessages = form.querySelectorAll('.invalid-feedback:not(.hidden)');

        errorFields.forEach(field => {
            _clearFieldError(field);
        });

        errorMessages.forEach(message => {
            message.classList.add('hidden');
        });
    }

    // Initialize Preline components
    function _initializePrelineComponents() {
        // Initialize Preline Select components
        if (typeof HSSelect !== 'undefined') {
            HSSelect.autoInit();
        }

        // Initialize other Preline components if needed
        if (typeof HSStaticMethods !== 'undefined') {
            HSStaticMethods.autoInit();
        }
    }

    // Public API
    return {
        init: function() {
            _componentFormValidation();
            _initializePrelineComponents();
        },
        validateForm: _validateForm,
        submitForm: _submitForm
    };
}();

// Initialize when DOM is ready (if not already initialized by offcanvas)
$(document).ready(function() {
    // Only initialize if the form exists and hasn't been initialized yet
    if (document.getElementById('contact-form') && typeof window.contactFormInitialized === 'undefined') {
        ContactForm.init();
        window.contactFormInitialized = true;
    }
});

// Global initialization function for offcanvas usage
window.initializeContactForm = function() {
    ContactForm.init();
    window.contactFormInitialized = true;
};
